<?php

namespace App\Http\Controllers;

use App\Library\Helper\AcademicHelper;
use App\Models\AcademicWeek;
use App\Models\DisciplineRecord;
use App\Models\StudentClassAttendance;
use App\Models\Timetable;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\MobileNotification;
use App\Models\MobileDevice;
use App\Library\Repository\MobileNotificationRepository;
use App\Library\Repository\MobileApiRepository;
use App\Http\Requests\MobileStudentHealthRecordRequest;
use App\Http\Requests\MobileStaffHealthRecordRequest;
use App\Http\Requests\MobileGuestHealthRecordRequest;
use App\Http\Requests\MobileStudentHealthInfoRequest;

class MobileApiController extends Controller
{
    protected MobileNotificationRepository $mobileNotification;

    public function __construct(MobileNotificationRepository $mobileNotification)
    {
        $this->mobileNotification = $mobileNotification;
    }
    public function checkStudentCredentials()
    {
        $username = request('username');
        $password = request('password');
        $deviceTokenRaw = request('deviceToken');
        $deviceType = request('deviceType', 'ios'); // Use 'deviceType' parameter, default to 'ios'
        $deviceName = request('deviceName', ''); // Get device name

        // Validate device token
        if (empty($deviceTokenRaw)) {
            return response()->json(['error' => 'Device token is required'], 400);
        }

        $deviceToken = base64_decode($deviceTokenRaw);

        // Validate decoded device token
        if (empty($deviceToken) || strlen($deviceToken) < 10) {
            return response()->json(['error' => 'Invalid device token'], 400);
        }

        // Validate device type
        if (!in_array(strtolower($deviceType), ['ios', 'android'])) {
            return response()->json(['error' => 'Invalid device type. Must be ios or android'], 400);
        }

        $rd = null;
        $arr = null;
        if (\Auth::attempt(array('username' => $username, 'password' => $password))) {
            $userInfo = User::find($username);

            // Check if device already exists for this user and device token
            $existingDevice = MobileDevice::where('student_id', $username)
                                        ->where('device_token', $deviceToken)
                                        ->where('user_type', 'student')
                                        ->first();

            if ($existingDevice) {
                // Update existing device with new auth code, device info and timestamp
                $existingDevice->update([
                    'auth_code' => uniqid(),
                    'device_type' => strtolower($deviceType),
                    'device_name' => $deviceName,
                    'last_login' => now(),
                    'updated_at' => now()
                ]);
                $device = $existingDevice;
            } else {
                // Create new device record
                $device = MobileDevice::create([
                    'auth_code' =>  uniqid(),
                    'student_id'    => $username,
                    'device_type'   => strtolower($deviceType),
                    'device_name'   => $deviceName,
                    'user_type'   => 'student',
                    'device_token' => $deviceToken,
                    'last_login' => now()
                ]);
            }

            if ($device) {
                // Check if user is a homeroom teacher
                $isHomeroom = false;
                if ($userInfo->user_type === 'staff') {
                    $academicHelper = new \App\Library\Helper\AcademicHelper();
                    $homeroomClassrooms = $academicHelper->homeroomClassrooms($userInfo->id);
                    $isHomeroom = $homeroomClassrooms->count() > 0;
                }

                // Get branch information
                $branchInfo = null;
                if ($userInfo->branch_id) {
                    $branch = \App\Models\Branch::find($userInfo->branch_id);
                    if ($branch) {
                        $branchInfo = [
                            'branch_id' => $branch->branch_id,
                            'branch_name' => $branch->branch_name,
                            'branch_description' => $branch->branch_description ?? null,
                            'branch_code' => $branch->branch_code ?? null,
                            'branch_address' => $branch->branch_address ?? null
                        ];
                    }
                }

                $arr = array(
                    "name"      => $userInfo->name,
                    "photo"     => $userInfo->photo ? "https://sis.bfi.edu.mm" . $userInfo->photo : null,
                    "authCode"  => $device->auth_code,
                    "id"        => $username,
                    "user_id"   => $userInfo->id,
                    "username"  => $userInfo->username,
                    "email"     => $userInfo->email,
                    "mobile_phone" => $userInfo->mobile_phone ?? $userInfo->phone,
                    "user_type" => $userInfo->user_type,
                    "user_status" => $userInfo->user_status,
                    "gender"    => $userInfo->gender,
                    "birth_date" => $userInfo->birth_date,
                    "nationality" => $userInfo->nationality,
                    "address"   => $userInfo->address,
                    "emergency_contact" => $userInfo->emergency_contact,
                    "rfid"      => $userInfo->rfid,
                    "branch"    => $branchInfo,
                    "is_homeroom" => $isHomeroom,
                    "last_login" => $device->last_login,
                    "device_info" => [
                        'device_type' => $device->device_type,
                        'device_name' => $device->device_name
                    ]
                );
            }
            $returnData = $arr;
        } else {
            $returnData = 0;
        }
        return $returnData;
    }

    public function getNotifications()
    {
        $userArr = explode('|', request('username'));
        $notifications = MobileNotification::whereIn('user_id', $userArr)
            ->orWhere('notification_type', 'all')
            ->groupBy('notification_uid')
            ->orderBy('created_at', 'desc')
            ->take(30)
            ->get();

        $myObj = new \stdClass;
        $jsonString = null;
        $counter = 0;

        foreach ($notifications as $key => $notification) {
            $notificationTimeArr = explode(" ", $notification->created_at);
            $myObj->notificationTitle = $notification->notification_title;
            $myObj->notificationBody = $notification->notification_body;
            $myObj->notificationDate = $notificationTimeArr[0];
            $myObj->notificationTime = $notificationTimeArr[1];
            $myObj->notificationType = $notification->notification_type;
            $myJson = json_encode($myObj);
            $jsonString = $jsonString . $myJson;
            $counter++;
        }

        if ($counter == 0) {
            $myObj->notificationTitle = "No new notifications";
            $myObj->notificationBody = "We could not find any new notifications.";
            $myObj->notificationDate = " ";
            $myObj->notificationTime = " ";
            $myObj->notificationType = "all";
            $myJson = json_encode($myObj);
            $jsonString = $jsonString . $myJson;
        }
        $jsonString = str_replace("}{", "},{", $jsonString);
        $jsonString = "[" . $jsonString . "]";
        return $jsonString;
    }

    public function checkStaffCredentials()
    {
        $username = request('username');
        $password = request('password');
        $deviceTokenRaw = request('deviceToken');
        $deviceType = request('deviceType', 'ios'); // Use 'deviceType' parameter, default to 'ios'
        $deviceName = request('deviceName', ''); // Get device name

        // Validate device token
        if (empty($deviceTokenRaw)) {
            return response()->json(['error' => 'Device token is required'], 400);
        }

        $deviceToken = base64_decode($deviceTokenRaw);

        // Validate decoded device token
        if (empty($deviceToken) || strlen($deviceToken) < 10) {
            return response()->json(['error' => 'Invalid device token'], 400);
        }

        // Validate device type
        if (!in_array(strtolower($deviceType), ['ios', 'android'])) {
            return response()->json(['error' => 'Invalid device type. Must be ios or android'], 400);
        }

        $rd = null;
        $arr = null;
        if (\Auth::attempt(array('username' => $username, 'password' => $password))) {
            $userInfo = User::where('username', $username)->first();

            // Get user roles with branch and role details
            $userRoles = \App\Models\UserRole::leftJoin('roles', 'roles.role_id', 'roles_user.role_id')
                ->leftJoin('branches', 'branches.branch_id', 'roles_user.branch_id')
                ->where('roles_user.user_id', $userInfo->id)
                ->select(
                    'roles_user.role_user_id',
                    'roles_user.role_id',
                    'roles_user.branch_id',
                    'roles.role_name',
                    'branches.branch_name'
                )
                ->get();

            // Format roles data
            $rolesData = [];
            foreach ($userRoles as $role) {
                $rolesData[] = [
                    'role_user_id' => $role->role_user_id,
                    'role_id' => $role->role_id,
                    'role_name' => $role->role_name,
                    'branch_id' => $role->branch_id,
                    'branch_name' => $role->branch_name
                ];
            }

            // Check if device already exists for this user and device token
            $existingDevice = MobileDevice::where('student_id', $userInfo->id)
                                        ->where('device_token', $deviceToken)
                                        ->where('user_type', 'staff')
                                        ->first();

            if ($existingDevice) {
                // Update existing device with new auth code, device info and timestamp
                $existingDevice->update([
                    'auth_code' => uniqid(),
                    'device_type' => strtolower($deviceType),
                    'device_name' => $deviceName,
                    'last_login' => now(),
                    'updated_at' => now()
                ]);
                $device = $existingDevice;
            } else {
                // Create new device record
                $device = MobileDevice::create([
                    'auth_code' =>  uniqid(),
                    'student_id'    => $userInfo->id,
                    'device_type'   => strtolower($deviceType),
                    'device_name'   => $deviceName,
                    'user_type'   => 'staff',
                    'device_token' => $deviceToken,
                    'last_login' => now()
                ]);
            }

            if ($device) {
                // Check if user is a homeroom teacher
                $isHomeroom = false;
                if ($userInfo->user_type === 'staff') {
                    $academicHelper = new \App\Library\Helper\AcademicHelper();
                    $homeroomClassrooms = $academicHelper->homeroomClassrooms($userInfo->id);
                    $isHomeroom = $homeroomClassrooms->count() > 0;
                }

                // Get branch information
                $branchInfo = null;
                if ($userInfo->branch_id) {
                    $branch = \App\Models\Branch::find($userInfo->branch_id);
                    if ($branch) {
                        $branchInfo = [
                            'branch_id' => $branch->branch_id,
                            'branch_name' => $branch->branch_name,
                            'branch_description' => $branch->branch_description ?? null,
                            'branch_code' => $branch->branch_code ?? null,
                            'branch_address' => $branch->branch_address ?? null
                        ];
                    }
                }

                $arr = array(
                    "name"      => $userInfo->name,
                    "photo"     => $userInfo->photo ? "https://sis.bfi.edu.mm" . $userInfo->photo : null,
                    "authCode"  => $device->auth_code,
                    "id"        => $username,
                    "user_id"   => $userInfo->id,
                    "username"  => $userInfo->username,
                    "email"     => $userInfo->email,
                    "mobile_phone" => $userInfo->mobile_phone ?? $userInfo->phone,
                    "user_type" => $userInfo->user_type,
                    "user_status" => $userInfo->user_status,
                    "gender"    => $userInfo->gender,
                    "birth_date" => $userInfo->birth_date,
                    "nationality" => $userInfo->nationality,
                    "address"   => $userInfo->address,
                    "emergency_contact" => $userInfo->emergency_contact,
                    "rfid"      => $userInfo->rfid,
                    "biometric_id" => $userInfo->biometric_id,
                    "admin"     => $userInfo->admin == 1 ? true : false,
                    "branch"    => $branchInfo,
                    "roles"     => $rolesData,
                    "total_roles" => count($rolesData),
                    "is_homeroom" => $isHomeroom,
                    "last_login" => $device->last_login,
                    "device_info" => [
                        'device_type' => $device->device_type,
                        'device_name' => $device->device_name
                    ]
                );
            }
            $returnData = $arr;
        } else {
            $returnData = 0;
        }
        return $returnData;
    }

    public function storeApiClassAttendance()
    {
        $timetableId = request('timetable');
        $attendance = request('attendance');
        $topic      = request('topic');

        $timetable = Timetable::find($timetableId);
        $attendanceInfo = explode("/", $attendance);
        $branchId = $timetable->branch_id;
        $academicYearId = $timetable->academic_year_id;
        $today = date('Y-m-d');
        $weekInfo = AcademicWeek::where('start_date', '<=', $today)
            ->where('end_date', '>=', $today)
            ->where('branch_id', $branchId)
            ->first();
        $currentWeek = $weekInfo->week;

        // Track notification results
        $notificationResults = [];
        $totalStudents = 0;
        $notificationsSent = 0;
        $notificationsFailed = 0;

        foreach ($attendanceInfo as $info) {
            if ($info != '') {
                $arr = explode("|", $info);
                $totalStudents++;

                $attendanceRecord = StudentClassAttendance::updateOrCreate(
                    [
                        'academic_year_id'  => $academicYearId,
                        'week_day'          => $timetable->week_day,
                        'week_time'         => $timetable->week_time,
                        'subject_id'        => $timetable->subject_id,
                        'teacher_id'        => $timetable->user_id,
                        'grade_id'          => $timetable->grade_id,
                        'week'              => $currentWeek,
                        'student_id'        => $arr[0]
                    ],
                    [
                        'date'              => date('Y-m-d'),
                        'attendance_status' => $arr[1],
                        'attendance_note'   => $arr[2]
                    ]
                );

                // Enhanced attendance notifications for absent and late students
                $notificationResult = $this->sendAttendanceNotification($arr, $timetable, $attendanceRecord);

                if ($notificationResult['notification_sent']) {
                    $notificationsSent++;
                } elseif ($notificationResult['notification_attempted']) {
                    $notificationsFailed++;
                }

                $notificationResults[] = [
                    'student_id' => $arr[0],
                    'attendance_status' => $arr[1],
                    'notification_sent' => $notificationResult['notification_sent'],
                    'notification_attempted' => $notificationResult['notification_attempted'],
                    'notification_message' => $notificationResult['message']
                ];
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Attendance stored successfully',
            'data' => [
                'total_students' => $totalStudents,
                'notifications_sent' => $notificationsSent,
                'notifications_failed' => $notificationsFailed,
                'notification_details' => $notificationResults
            ]
        ]);
    }

    /**
     * Send attendance notifications for absent and late students
     */
    private function sendAttendanceNotification($attendanceData, $timetable, $attendanceRecord)
    {
        $studentId = $attendanceData[0];
        $attendanceStatus = $attendanceData[1];
        $attendanceNote = $attendanceData[2] ?? '';

        // Only send notifications for absent and late students
        if (!in_array($attendanceStatus, ['absent', 'late'])) {
            return [
                'notification_sent' => false,
                'notification_attempted' => false,
                'message' => 'No notification needed for status: ' . $attendanceStatus
            ];
        }

        // Check if student is already marked absent for the entire day
        // If so, don't send individual class absence notifications
        if ($attendanceStatus === 'absent') {
            $currentDate = date('Y-m-d');
            $ah = new AcademicHelper();
            $academicYearId = $ah->branchAcademicYear($timetable->branch_id);

            $dailyAttendance = \App\Models\StudentAttendance::where('student_id', $studentId)
                ->where('date', $currentDate)
                ->where('academic_year_id', $academicYearId)
                ->where('attendance_status', 'absent')
                ->first();

            if ($dailyAttendance) {
                return [
                    'notification_sent' => false,
                    'notification_attempted' => false,
                    'message' => 'Student already marked absent for the day - no class notification sent'
                ];
            }
        }

        try {
            // Get student information
            $student = User::where('id', $studentId)
                          ->where('user_status', 1)
                          ->first();

            if (!$student) {
                return [
                    'notification_sent' => false,
                    'notification_attempted' => true,
                    'message' => 'Student not found: ' . $studentId
                ];
            }

            // Get subject and grade information
            $subject = \App\Models\Subject::find($timetable->subject_id);
            $grade = \App\Models\ElectiveGrade::where('grade_id', $timetable->grade_id)->first();
            $teacher = User::find($timetable->user_id);

            // Prepare notification data
            $subjectName = $subject ? $subject->subject_name : 'Unknown Subject';
            $gradeName = $grade ? $grade->grade_name : 'Unknown Grade';
            $teacherName = $teacher ? $teacher->name : 'Unknown Teacher';
            $currentDate = date('Y-m-d');
            $currentTime = date('H:i');

            // Create different messages based on attendance status
            $title = $attendanceStatus === 'absent' ? 'Absence Alert' : 'Late Arrival Alert';

            if ($attendanceStatus === 'absent') {
                $message = "{$student->name} was marked as ABSENT in {$subjectName} ({$gradeName}) class on {$currentDate} at {$currentTime}.";
                $priority = 'high';
                $category = 'attendance';
            } else { // late
                $message = "{$student->name} was marked as LATE in {$subjectName} ({$gradeName}) class on {$currentDate} at {$currentTime}.";
                $priority = 'normal';
                $category = 'attendance';
            }

            // Add note if provided
            if (!empty($attendanceNote)) {
                $message .= " Note: {$attendanceNote}";
            }

            // Send notification to student
            $notificationData = [
                'student' => $studentId,
                'type' => 'attendance_' . $attendanceStatus,
                'title' => $title,
                'message' => $message,
                'user_type' => 'student',
                'priority' => $priority,
                'category' => $category,
                'data' => [
                    'attendance_id' => $attendanceRecord->attendance_id ?? null,
                    'attendance_status' => $attendanceStatus,
                    'subject_name' => $subjectName,
                    'grade_name' => $gradeName,
                    'teacher_name' => $teacherName,
                    'date' => $currentDate,
                    'time' => $currentTime,
                    'period' => $timetable->week_time,
                    'note' => $attendanceNote
                ]
            ];

            // Send real-time notification
            $this->mobileNotification->sendRealTime($notificationData);

            return [
                'notification_sent' => true,
                'notification_attempted' => true,
                'message' => "Notification sent successfully for {$attendanceStatus} status"
            ];

        } catch (\Exception $e) {
            return [
                'notification_sent' => false,
                'notification_attempted' => true,
                'message' => 'Failed to send notification: ' . $e->getMessage()
            ];
        }
    }



    /**
     * Store BPS records with comprehensive validation and business logic
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeBps(Request $request)
    {
        // Optional CSRF token validation for enhanced security
        $csrfToken = $request->input('csrf_token');
        if ($csrfToken) {
            // Validate CSRF token if provided
            if (!hash_equals(csrf_token(), $csrfToken)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid CSRF token',
                    'error_code' => 'CSRF_TOKEN_MISMATCH'
                ], 422);
            }
        }

        // Validate auth_code for mobile authentication
        $authCode = $request->input('auth_code');
        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required',
                'error_code' => 'AUTH_CODE_REQUIRED'
            ], 401);
        }

        // Verify the auth_code belongs to a valid staff member
        $device = MobileDevice::where('auth_code', $authCode)->first();
        if (!$device || $device->user_type !== 'staff') {
            return response()->json([
                'success' => false,
                'message' => 'Invalid authentication code or insufficient permissions',
                'error_code' => 'INVALID_AUTH_CODE'
            ], 401);
        }

        // Validate required parameters
        $requiredFields = ['branch_id', 'case_type', 'date'];
        foreach ($requiredFields as $field) {
            if (!$request->has($field) || $request->input($field) === null) {
                return response()->json([
                    'success' => false,
                    'message' => "Missing required field: {$field}",
                    'error_code' => 'MISSING_REQUIRED_FIELD'
                ], 400);
            }
        }

        // Validate case_type
        $caseType = $request->input('case_type');
        if (!in_array($caseType, ['0', '1'])) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid case_type. Must be "0" for PRS or "1" for DPS',
                'error_code' => 'INVALID_CASE_TYPE'
            ], 400);
        }

        // Validate date format and ensure it's not in the future
        $date = $request->input('date');
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid date format. Use YYYY-MM-DD',
                'error_code' => 'INVALID_DATE_FORMAT'
            ], 400);
        }

        if (strtotime($date) > strtotime(date('Y-m-d'))) {
            return response()->json([
                'success' => false,
                'message' => 'Date cannot be in the future',
                'error_code' => 'FUTURE_DATE_NOT_ALLOWED'
            ], 400);
        }

        $repository = new MobileApiRepository();

        // Unified data structure that handles both single and multiple BPS records
        $data = [
            'branch_id' => $request->input('branch_id'),
            'case_type' => $caseType,
            'date' => $date,
            'note' => $request->input('note', ''),
            'user_id' => $device->student_id, // Use authenticated user ID from device
            'academic_semester' => $request->input('academic_semester'), // Optional, will use current if not provided

            // Multiple format (arrays)
            'students' => $request->input('students', []),
            'items' => $request->input('items', []),

            // Single format (individual IDs)
            'student_id' => $request->input('student_id'),
            'item_id' => $request->input('item_id'),

            // Legacy format support
            'student' => $request->input('student', []),
            'dps_case' => $request->input('dps_case'),
            'prs_case' => $request->input('prs_case'),

            // Security context
            'auth_code' => $authCode,
            'csrf_token_provided' => !empty($csrfToken)
        ];

        $result = $repository->storeBps($data);

        // Add notification status tracking to the response
        if ($result['success'] && isset($result['results'])) {
            $notificationsSent = 0;
            $notificationsFailed = 0;
            $notificationDetails = [];

            foreach ($result['results'] as $record) {
                if (isset($record['notification_sent'])) {
                    if ($record['notification_sent']) {
                        $notificationsSent++;
                    } else {
                        $notificationsFailed++;
                    }

                    $notificationDetails[] = [
                        'student_id' => $record['student_id'],
                        'bps_record_id' => $record['record_id'],
                        'notification_sent' => $record['notification_sent'],
                        'notification_message' => $record['notification_message'] ?? 'BPS notification processed'
                    ];
                }
            }

            $result['notification_summary'] = [
                'total_notifications_sent' => $notificationsSent,
                'total_notifications_failed' => $notificationsFailed,
                'notification_details' => $notificationDetails
            ];
        }

        // Add security information to response
        $result['security_info'] = array_merge($result['security_info'] ?? [], [
            'csrf_token_validated' => !empty($csrfToken),
            'auth_code_validated' => true,
            'authenticated_user_id' => $device->student_id,
            'request_timestamp' => now()->toISOString()
        ]);

        // Return appropriate HTTP status code based on result
        $statusCode = $result['success'] ? 200 : 400;
        if (isset($result['error_code'])) {
            switch ($result['error_code']) {
                case 'INVALID_AUTH_CODE':
                case 'PERMISSION_DENIED':
                    $statusCode = 401;
                    break;
                case 'INVALID_ITEM':
                case 'NO_STUDENTS':
                case 'NO_ITEM':
                    $statusCode = 404;
                    break;
                case 'SERVER_ERROR':
                    $statusCode = 500;
                    break;
            }
        }

        return response()->json($result, $statusCode);
    }

    /**
     * Delete BPS record with proper authentication and permission checking
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteBps()
    {
        try {
            // Handle both parameter naming conventions
            $bpsId = request('bpsId') ?? request('dicipline_record_id') ?? request('discipline_record_id');
            $authCode = request('authCode') ?? request('authcode') ?? request('auth_code');

            // Validate required parameters
            if (!$bpsId) {
                return response()->json([
                    'success' => false,
                    'message' => 'BPS ID is required (bpsId, dicipline_record_id, or discipline_record_id)',
                    'error_code' => 'BPS_ID_REQUIRED'
                ], 400);
            }

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication code is required (authCode, authcode, or auth_code)',
                    'error_code' => 'AUTH_CODE_REQUIRED'
                ], 401);
            }

            // Verify the auth_code belongs to a valid staff member
            $device = MobileDevice::where('auth_code', $authCode)->first();
            if (!$device || $device->user_type !== 'staff') {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid authentication code or insufficient permissions',
                    'error_code' => 'INVALID_AUTH_CODE'
                ], 401);
            }

            // Get the staff user information
            $staffUser = User::find($device->student_id);
            if (!$staffUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Staff user not found',
                    'error_code' => 'USER_NOT_FOUND'
                ], 404);
            }

            // Check if BPS record exists
            $bpsRecord = DisciplineRecord::find($bpsId);
            if (!$bpsRecord) {
                return response()->json([
                    'success' => false,
                    'message' => 'BPS record not found',
                    'error_code' => 'BPS_NOT_FOUND'
                ], 404);
            }

            // Use the BPS repository for proper deletion logic (handles award points)
            $academicHelper = new \App\Library\Helper\AcademicHelper();
            $mobileNotificationRepository = new \App\Library\Repository\MobileNotificationRepository($academicHelper);
            $bpsRepository = new \App\Library\Repository\BpsRepository($academicHelper, $mobileNotificationRepository);
            $bpsRepository->deleteBps($bpsId);

            return response()->json([
                'success' => true,
                'message' => 'BPS record deleted successfully',
                'data' => [
                    'deleted_bps_id' => $bpsId,
                    'deleted_by' => $staffUser->name,
                    'deleted_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {


            return response()->json([
                'success' => false,
                'message' => 'Failed to delete BPS record',
                'error_code' => 'DELETION_FAILED',
                'error_details' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if the authenticated user has permission to delete BPS records
     * This endpoint helps the frontend determine whether to show delete buttons
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkBpsDeletePermission()
    {
        try {
            $authCode = request('authCode');

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication code is required',
                    'error_code' => 'AUTH_CODE_REQUIRED'
                ], 401);
            }

            // Verify the auth_code belongs to a valid staff member
            $device = MobileDevice::where('auth_code', $authCode)->first();
            if (!$device || $device->user_type !== 'staff') {
                return response()->json([
                    'success' => false,
                    'has_permission' => false,
                    'message' => 'Invalid authentication code or not a staff member'
                ]);
            }

            // Get the staff user
            $staffUser = User::find($device->student_id);
            if (!$staffUser) {
                return response()->json([
                    'success' => false,
                    'has_permission' => false,
                    'message' => 'Staff user not found'
                ]);
            }

            // Check BPS delete permission (module 56, action 2)
            $module = 56;
            $action = 2;
            $colName = "p" . $action;
            $userId = $staffUser->id;

            // Get branch ID using the same logic as getTeacherBpsData
            $branchId = null;
            if ($staffUser->branch_id) {
                $branchId = $staffUser->branch_id;
            } else {
                // Fallback: get first branch from user's branches
                $userBranch = \DB::table('users_branches')
                    ->where('user_id', $userId)
                    ->first();
                if ($userBranch) {
                    $branchId = $userBranch->branch_id;
                }
            }

            if (!$branchId) {
                return response()->json([
                    'success' => false,
                    'has_permission' => false,
                    'message' => 'No branch information available for permission check'
                ]);
            }

            // Check permission
            $hasPermission = \App\Models\Permission::leftJoin('roles_user', 'roles_user.role_id', 'permissions.role_id')
                                                  ->where('permissions.module_id', $module)
                                                  ->where('roles_user.user_id', $userId)
                                                  ->where('roles_user.branch_id', $branchId)
                                                  ->where('permissions.' . $colName, 1)
                                                  ->count() > 0;

            return response()->json([
                'success' => true,
                'has_permission' => $hasPermission,
                'permission_details' => [
                    'module_id' => $module,
                    'action_id' => $action,
                    'permission_name' => 'BPS Delete Permission',
                    'user_id' => $userId,
                    'user_name' => $staffUser->name,
                    'branch_id' => $branchId
                ]
            ]);

        } catch (\Exception $e) {


            return response()->json([
                'success' => false,
                'has_permission' => false,
                'message' => 'Permission check failed',
                'error_code' => 'PERMISSION_CHECK_FAILED'
            ], 500);
        }
    }





    /**
     * Mark homework as viewed when student opens it
     */
    public function markHomeworkAsViewed(Request $request)
    {
        $authCode = $request->input('auth_code');
        $detailId = $request->input('detail_id');

        if (!$authCode || !$detailId) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code and detail ID are required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->markHomeworkAsViewed($authCode, $detailId);
    }

    /**
     * Submit homework by student
     */
    public function submitHomework(Request $request)
    {
        $authCode = $request->input('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        // Validate required fields
        $request->validate([
            'detail_id' => 'required|integer',
            'reply_file' => 'nullable|string',
            'reply_data' => 'nullable|string'
        ]);

        // At least one submission field is required
        if (empty($request->input('reply_file')) && empty($request->input('reply_data'))) {
            return response()->json([
                'success' => false,
                'message' => 'At least one submission (file or data) is required'
            ], 400);
        }

        $data = [
            'detail_id' => $request->input('detail_id'),
            'reply_file' => $request->input('reply_file'),
            'reply_data' => $request->input('reply_data')
        ];

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->submitHomework($authCode, $data);
    }

    /**
     * Mark homework as done (completed) without submission
     */
    public function markHomeworkAsDone(Request $request)
    {
        $authCode = $request->input('auth_code');
        $detailId = $request->input('detail_id');

        if (!$authCode || !$detailId) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code and detail ID are required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->markHomeworkAsDone($authCode, $detailId);
    }

    /**
     * Upload file for homework submission
     */
    public function uploadHomeworkFile(Request $request)
    {
        $authCode = $request->input('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        // Validate file upload
        $request->validate([
            'file' => 'required|file|max:10240', // 10MB max
        ]);

        $file = $request->file('file');

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->uploadHomeworkFile($authCode, $file);
    }

    /**
     * Update/resubmit homework after teacher feedback
     */
    public function updateHomeworkSubmission(Request $request)
    {
        $authCode = $request->input('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        // Validate required fields
        $request->validate([
            'detail_id' => 'required|integer',
            'reply_file' => 'nullable|string',
            'reply_data' => 'nullable|string'
        ]);

        // At least one submission field is required for update
        if (empty($request->input('reply_file')) && empty($request->input('reply_data'))) {
            return response()->json([
                'success' => false,
                'message' => 'At least one submission (file or data) is required for update'
            ], 400);
        }

        $data = [
            'detail_id' => $request->input('detail_id'),
            'reply_file' => $request->input('reply_file'),
            'reply_data' => $request->input('reply_data')
        ];

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->updateHomeworkSubmission($authCode, $data);
    }

    /**
     * Get teacher's homework list
     */
    public function getTeacherHomeworkList(Request $request)
    {
        $authCode = $request->query('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getTeacherHomeworkList($authCode);
    }

    /**
     * Get homework details with student submissions for teacher review
     */
    public function getTeacherHomeworkDetails(Request $request)
    {
        $authCode = $request->query('auth_code');
        $homeworkId = $request->query('homework_id');

        if (!$authCode || !$homeworkId) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code and homework ID are required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getTeacherHomeworkDetails($authCode, $homeworkId);
    }

    /**
     * Provide teacher feedback on student homework submission
     */
    public function provideHomeworkFeedback(Request $request)
    {
        $authCode = $request->input('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        // Validate required fields
        $request->validate([
            'detail_id' => 'required|integer',
            'comment' => 'required|string|min:1'
        ]);

        $data = [
            'detail_id' => $request->input('detail_id'),
            'comment' => $request->input('comment')
        ];

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->provideHomeworkFeedback($authCode, $data);
    }

    /**
     * Create new homework assignment
     */
    public function createHomeworkAssignment(Request $request)
    {
        $authCode = $request->input('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        // Validate required fields
        $request->validate([
            'title' => 'required|string|min:1',
            'grade_id' => 'required|integer',
            'students' => 'required|array|min:1',
            'students.*' => 'integer',
            'deadline' => 'required|date',
            'homework_data' => 'nullable|string',
            'homework_files' => 'nullable|string',
            'homework_video_links' => 'nullable|string'
        ]);

        $data = [
            'title' => $request->input('title'),
            'grade_id' => $request->input('grade_id'),
            'students' => $request->input('students'),
            'deadline' => $request->input('deadline'),
            'homework_data' => $request->input('homework_data'),
            'homework_files' => $request->input('homework_files'),
            'homework_video_links' => $request->input('homework_video_links')
        ];

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->createHomeworkAssignment($authCode, $data);
    }

    /**
     * Close homework assignment(s) - marks homework as inactive instead of deleting
     */
    public function closeHomeworkAssignment(Request $request)
    {
        $authCode = $request->input('auth_code');
        $homeworkId = $request->input('homework_id');
        $homeworkIds = $request->input('homework_ids');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        // Support both single and multiple closing
        $idsToClose = null;
        if ($homeworkIds && is_array($homeworkIds)) {
            // Multiple closing
            $idsToClose = $homeworkIds;
        } elseif ($homeworkId) {
            // Single closing
            $idsToClose = $homeworkId;
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Either homework_id or homework_ids array is required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->closeHomeworkAssignment($authCode, $idsToClose);
    }

    /**
     * Get teacher's classes and students for homework assignment
     */
    public function getTeacherClassesForHomework(Request $request)
    {
        $authCode = $request->query('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getTeacherClassesForHomework($authCode);
    }

    /**
     * Get teacher's classes and students for attendance taking
     */
    public function getTeacherClassesForAttendance(Request $request)
    {
        $authCode = $request->query('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getTeacherClassesForAttendance($authCode);
    }

    /**
     * Get homeroom teacher's assigned classrooms
     */
    public function getHomeroomClassrooms(Request $request)
    {
        $authCode = $request->query('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getHomeroomClassrooms($authCode);
    }

    /**
     * Get students in a specific homeroom classroom
     */
    public function getHomeroomStudents(Request $request)
    {
        $authCode = $request->query('auth_code');
        $classroomId = $request->query('classroom_id');

        if (!$authCode || !$classroomId) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code and classroom ID are required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getHomeroomStudents($authCode, $classroomId);
    }

    /**
     * Get homeroom attendance summary for a specific date
     */
    public function getHomeroomAttendance(Request $request)
    {
        $authCode = $request->query('auth_code');
        $classroomId = $request->query('classroom_id');
        $date = $request->query('date'); // Optional, defaults to today

        if (!$authCode || !$classroomId) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code and classroom ID are required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getHomeroomAttendance($authCode, $classroomId, $date);
    }

    /**
     * Get homeroom discipline records summary
     */
    public function getHomeroomDiscipline(Request $request)
    {
        $authCode = $request->query('auth_code');
        $classroomId = $request->query('classroom_id');
        $startDate = $request->query('start_date'); // Optional
        $endDate = $request->query('end_date'); // Optional

        if (!$authCode || !$classroomId) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code and classroom ID are required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getHomeroomDiscipline($authCode, $classroomId, $startDate, $endDate);
    }

    /**
     * Get detailed student profile for homeroom teacher
     */
    public function getHomeroomStudentProfile(Request $request)
    {
        $authCode = $request->query('auth_code');
        $studentId = $request->query('student_id');

        if (!$authCode || !$studentId) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code and student ID are required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getHomeroomStudentProfile($authCode, $studentId);
    }

    /**
     * ========================================
     * MOBILE MESSAGING API METHODS
     * ========================================
     */

    /**
     * Get conversations list for mobile user
     */
    public function getConversations(Request $request) {
        $authCode = $request->input('authCode');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getConversations($authCode);
    }

    /**
     * Get messages in a conversation
     */
    public function getConversationMessages(Request $request) {
        $authCode = $request->input('authCode');
        $conversationUuid = $request->input('conversation_uuid');
        $page = $request->input('page', 1);
        $limit = $request->input('limit', 50);

        if (!$authCode || !$conversationUuid) {
            return response()->json(['error' => 'authCode and conversation_uuid are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getConversationMessages($authCode, $conversationUuid, $page, $limit);
    }

    /**
     * Send a message to a conversation
     */
    public function sendMessage(Request $request) {
        $authCode = $request->input('authCode');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $data = $request->only([
            'conversation_uuid',
            'message_content',
            'message_type',
            'attachment_url'
        ]);

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->sendMessage($authCode, $data);
    }

    /**
     * Create a new conversation
     */
    public function createConversation(Request $request) {
        $authCode = $request->input('authCode');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $data = $request->only([
            'topic',
            'members'
        ]);

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->createConversation($authCode, $data);
    }

    /**
     * Get available users for messaging - DEPRECATED
     * Use getAvailableUsersForStudent() or getAvailableUsersForStaff() instead
     */
    public function getAvailableUsers(Request $request) {
        $authCode = $request->input('authCode');
        $userType = $request->input('user_type');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getAvailableUsers($authCode, $userType);
    }

    /**
     * Get available users for messaging - STUDENT VERSION (Restricted)
     *
     * Students can ONLY message:
     * - Their homeroom teacher
     * - Teachers who actually teach them (subject teachers)
     * - Head of section/department
     * - Librarian
     * - Their direct classmates (same classroom)
     *
     * Students CANNOT message:
     * - Other students from different classes
     * - Teachers who don't teach them
     * - Random staff members
     */
    public function getAvailableUsersForStudent(Request $request) {
        $authCode = $request->input('authCode');
        $userType = $request->input('user_type');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getAvailableUsersForStudent($authCode, $userType);
    }

    /**
     * Get available users for messaging - STAFF VERSION (Role-Based Access)
     *
     * Staff access is based on their role:
     * - HEAD OF SCHOOL: Can message all students, all staff, all parents in branch
     * - HEAD OF SECTION/DEPARTMENT: Can message all students in section, all staff, parents of section students
     * - HOMEROOM TEACHER: Can message their homeroom students and their parents only
     * - SUBJECT TEACHER: Can message only students who take their subjects
     * - GENERAL STAFF: Can message only other staff members
     */
    public function getAvailableUsersForStaff(Request $request) {
        $authCode = $request->input('authCode');
        $userType = $request->input('user_type');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getAvailableUsersForStaff($authCode, $userType);
    }

    /**
     * Search conversations and messages
     */
    public function searchMessages(Request $request) {
        $authCode = $request->input('authCode');
        $query = $request->input('query');
        $type = $request->input('type', 'all');

        if (!$authCode || !$query) {
            return response()->json(['error' => 'authCode and query are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->searchMessages($authCode, $query, $type);
    }

    /**
     * Mark messages as read in a conversation
     */
    public function markMessagesAsRead(Request $request) {
        $authCode = $request->input('authCode');
        $conversationUuid = $request->input('conversation_uuid');

        if (!$authCode || !$conversationUuid) {
            return response()->json(['error' => 'authCode and conversation_uuid are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->markMessagesAsRead($authCode, $conversationUuid);
    }

    /**
     * Mark a specific message as read
     */
    public function markMessageAsRead(Request $request) {
        $authCode = $request->input('authCode');
        $messageId = $request->input('message_id');

        if (!$authCode || !$messageId) {
            return response()->json(['error' => 'authCode and message_id are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->markMessageAsRead($authCode, $messageId);
    }

    /**
     * Get conversation members grouped by user type
     */
    public function getConversationMembers(Request $request) {
        $authCode = $request->input('authCode');
        $conversationUuid = $request->input('conversation_uuid');

        if (!$authCode || !$conversationUuid) {
            return response()->json(['error' => 'authCode and conversation_uuid are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getConversationMembers($authCode, $conversationUuid);
    }

    /**
     * Delete a conversation (only creator can delete)
     */
    public function deleteConversation(Request $request) {
        $authCode = $request->input('authCode');
        $conversationUuid = $request->input('conversation_uuid');

        if (!$authCode || !$conversationUuid) {
            return response()->json(['error' => 'authCode and conversation_uuid are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->deleteConversation($authCode, $conversationUuid);
    }

    /**
     * Leave a conversation (remove user from conversation)
     */
    public function leaveConversation(Request $request) {
        $authCode = $request->input('authCode');
        $conversationUuid = $request->input('conversation_uuid');

        if (!$authCode || !$conversationUuid) {
            return response()->json(['error' => 'authCode and conversation_uuid are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->leaveConversation($authCode, $conversationUuid);
    }

    /**
     * Delete a specific message (only sender can delete their own messages)
     */
    public function deleteMessage(Request $request) {
        $authCode = $request->input('authCode');
        $messageId = $request->input('message_id');

        if (!$authCode || !$messageId) {
            return response()->json(['error' => 'authCode and message_id are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->deleteMessage($authCode, $messageId);
    }

    /**
     * Upload attachment for message
     */
    public function uploadMessageAttachment(Request $request) {
        $authCode = $request->input('authCode');
        $file = $request->file('attachment');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        if (!$file) {
            return response()->json(['error' => 'attachment file is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->uploadMessageAttachment($authCode, $file);
    }

    // ========================================
    // HEALTH MANAGEMENT API METHODS
    // ========================================

    /**
     * Get student health records for mobile
     */
    public function getStudentHealthRecords(Request $request) {
        $authCode = $request->input('authCode');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getStudentHealthRecords($authCode);
    }

    /**
     * Get student health information for mobile
     */
    public function getStudentHealthInfo(Request $request) {
        $authCode = $request->input('authCode');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getStudentHealthInfo($authCode);
    }

    /**
     * Get teacher health data for mobile (staff only)
     */
    public function getTeacherHealthData(Request $request) {
        $authCode = $request->input('authCode');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getTeacherHealthData($authCode);
    }

    /**
     * Create student health record for mobile (staff only)
     */
    public function createStudentHealthRecord(MobileStudentHealthRecordRequest $request) {
        $data = $request->validated();
        $authCode = $data['authCode'];

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->createStudentHealthRecord($authCode, $data);
    }

    /**
     * Create staff health record for mobile (staff only)
     */
    public function createStaffHealthRecord(MobileStaffHealthRecordRequest $request) {
        $data = $request->validated();
        $authCode = $data['authCode'];

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->createStaffHealthRecord($authCode, $data);
    }

    /**
     * Create guest health record for mobile (staff only)
     */
    public function createGuestHealthRecord(MobileGuestHealthRecordRequest $request) {
        $data = $request->validated();
        $authCode = $data['authCode'];

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->createGuestHealthRecord($authCode, $data);
    }

    /**
     * Update student health information for mobile (staff only)
     */
    public function updateStudentHealthInfo(MobileStudentHealthInfoRequest $request) {
        $data = $request->validated();
        $authCode = $data['authCode'];

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->updateStudentHealthInfo($authCode, $data);
    }

    /**
     * Delete health record for mobile (staff only)
     */
    public function deleteHealthRecord(Request $request) {
        $authCode = $request->input('authCode');
        $recordType = $request->input('record_type');
        $recordId = $request->input('record_id');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        if (!$recordType || !$recordId) {
            return response()->json(['error' => 'record_type and record_id are required'], 400);
        }

        if (!in_array($recordType, ['student', 'staff', 'guest'])) {
            return response()->json(['error' => 'Invalid record_type. Must be student, staff, or guest'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->deleteHealthRecord($authCode, $recordType, $recordId);
    }

    /**
     * Get health lookup data for mobile (injuries, actions, medications)
     */
    public function getHealthLookupData(Request $request) {
        $authCode = $request->input('authCode');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getHealthLookupData($authCode);
    }
}
