<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MessageRead extends Model
{
    use HasFactory;
    
    protected $table = 'msg_message_reads';
    protected $primaryKey = 'read_id';
    protected $guarded = ['read_id'];

    protected $fillable = [
        'message_id',
        'user_id',
        'read_at'
    ];

    protected $casts = [
        'read_at' => 'datetime'
    ];

    /**
     * Get the message that was read
     */
    public function message()
    {
        return $this->belongsTo(ChatMessage::class, 'message_id', 'message_id');
    }

    /**
     * Get the user who read the message
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
