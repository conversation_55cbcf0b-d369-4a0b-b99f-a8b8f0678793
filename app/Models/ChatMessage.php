<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChatMessage extends Model
{
    use HasFactory;
    protected $table = 'msg_messages';
    protected $primaryKey = 'message_id';
    protected $guarded = ['message_id'];

    public function user() {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Check if message has been read by a specific user
     * Based on comparing message timestamp with user's last read time in conversation
     */
    public function isReadBy($userId)
    {
        $chatUser = \App\Models\ChatUser::where('chat_id', $this->chat_id)
            ->where('user_id', $userId)
            ->first();

        if (!$chatUser) {
            return false;
        }

        // If user's updated_at in chat_users is after message created_at, it's read
        return $chatUser->updated_at >= $this->created_at;
    }
}
