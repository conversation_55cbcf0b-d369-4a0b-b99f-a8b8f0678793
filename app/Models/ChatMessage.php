<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChatMessage extends Model
{
    use HasFactory;
    protected $table = 'msg_messages';
    protected $primaryKey = 'message_id';
    protected $guarded = ['message_id'];

    public function user() {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the read records for this message
     */
    public function reads()
    {
        return $this->hasMany(MessageRead::class, 'message_id', 'message_id');
    }

    /**
     * Check if message has been read by a specific user
     */
    public function isReadBy($userId)
    {
        return $this->reads()->where('user_id', $userId)->exists();
    }

    /**
     * Mark message as read by a specific user
     */
    public function markAsReadBy($userId)
    {
        return $this->reads()->firstOrCreate([
            'user_id' => $userId
        ], [
            'read_at' => now()
        ]);
    }
}
