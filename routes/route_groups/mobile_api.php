<?php

use App\Models\User;

use App\Library\Repository\MobileApiRepository;

Route::get('/mobile-api/check-student-credentials/', '\App\Http\Controllers\MobileApiController@checkStudentCredentials');

Route::get('/mobile-api/check-staff-credentials/', '\App\Http\Controllers\MobileApiController@checkStaffCredentials');

/*
|--------------------------------------------------------------------------
| Notification API Routes
|--------------------------------------------------------------------------
| Multiple notification endpoints for different use cases:
|
| 1. /mobile-api/get-notifications/ - Legacy API (username param)
| 2. /mobile-api/get-notifications-alt/ - Alternative implementation
| 3. /mobile-api/get-notifications-view/ - HTML view for mobile web
| 4. /mobile-api/notifications/list - Modern API (authCode param) - RECOMMENDED
|
*/

// Legacy notification API - kept for backward compatibility
Route::get('/mobile-api/get-notifications/', '\App\Http\Controllers\MobileApiController@getNotifications');

Route::get('/mobile-api/messages/', function () {
    return view('partial_view.mobile.messages.main', ['authCode' => $_GET['authCode']]);
});

// ========================================
// MOBILE MESSAGING API ROUTES
// ========================================

// Get conversations list
Route::get('/mobile-api/messaging/conversations', '\App\Http\Controllers\MobileApiController@getConversations');

// Get messages in a conversation
Route::get('/mobile-api/messaging/conversation/messages', '\App\Http\Controllers\MobileApiController@getConversationMessages');

// Get conversation members grouped by user type
Route::get('/mobile-api/messaging/conversation/members', '\App\Http\Controllers\MobileApiController@getConversationMembers');

// Send a message
Route::post('/mobile-api/messaging/send-message', '\App\Http\Controllers\MobileApiController@sendMessage');

// Create a new conversation
Route::post('/mobile-api/messaging/create-conversation', '\App\Http\Controllers\MobileApiController@createConversation');

// Get available users for messaging (deprecated - use specific endpoints below)
Route::get('/mobile-api/messaging/available-users', '\App\Http\Controllers\MobileApiController@getAvailableUsers');

// Get available users for messaging - Student version (restricted access)
Route::get('/mobile-api/messaging/available-users/student', '\App\Http\Controllers\MobileApiController@getAvailableUsersForStudent');

// Get available users for messaging - Staff version (full access)
Route::get('/mobile-api/messaging/available-users/staff', '\App\Http\Controllers\MobileApiController@getAvailableUsersForStaff');

// Search conversations and messages
Route::get('/mobile-api/messaging/search', '\App\Http\Controllers\MobileApiController@searchMessages');

// Mark messages as read
Route::post('/mobile-api/messaging/mark-read', '\App\Http\Controllers\MobileApiController@markMessagesAsRead');

// Mark individual message as read
Route::post('/mobile-api/messaging/mark-message-read', '\App\Http\Controllers\MobileApiController@markMessageAsRead');

// Delete conversation (only creator can delete)
Route::delete('/mobile-api/messaging/conversation/delete', '\App\Http\Controllers\MobileApiController@deleteConversation');

// Leave conversation (remove user from conversation)
Route::post('/mobile-api/messaging/conversation/leave', '\App\Http\Controllers\MobileApiController@leaveConversation');

// Delete a specific message (only sender can delete their own messages)
Route::delete('/mobile-api/messaging/message/delete', '\App\Http\Controllers\MobileApiController@deleteMessage');

// Upload message attachment
Route::post('/mobile-api/messaging/upload-attachment', '\App\Http\Controllers\MobileApiController@uploadMessageAttachment');

Route::get('/mobile-api/get-student-timetable/', function () {
    return view('partial_view.mobile.timetable', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/get-student-timetable2/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->getStudentTimetable($_GET['authCode']);
});

Route::get('/mobile-api/get-student-attendance/', function () {
    return view('partial_view.mobile.attendance', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/get-student-attendance-data/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->getStudentAttendanceData($_GET['authCode']);
});

Route::get('/mobile-api/get-student-bps/', function () {
    return view('partial_view.mobile.bps', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/get-student-bps-data/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->getStudentBpsData($_GET['authCode']);
});

Route::get('/mobile-api/homework/detail/', function () {
    return view('partial_view.student.homework.detail', ['uuid' => $_GET['uuid'], 'stdid' => $_GET['stdid']]);
});

Route::get('/mobile-api/get-student-library/', function () {
    return view('partial_view.mobile.library', ['authCode' => $_GET['authCode']]);
});

// Enhanced student library data API
Route::get('/mobile-api/student/library-data', function () {
    try {
        $authCode = request('authCode');

        if (!$authCode) {
            return response()->json(['error' => 'Authentication code is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getStudentLibraryData($authCode);

    } catch (Exception $e) {
        return response()->json(['error' => 'Server error: ' . $e->getMessage()], 500);
    }
});

Route::get('/mobile-api/get-student-assessment/', function () {
    return view('partial_view.mobile.assessment', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/get-student-health/', function () {
    return view('partial_view.mobile.health', ['authCode' => $_GET['authCode']]);
});

// ========================================
// HEALTH MANAGEMENT API ROUTES
// ========================================

// Student health endpoints (for students to view their own health data)
Route::get('/mobile-api/health/student/records', '\App\Http\Controllers\MobileApiController@getStudentHealthRecords');
Route::get('/mobile-api/health/student/info', '\App\Http\Controllers\MobileApiController@getStudentHealthInfo');

// Staff health endpoints (for staff to manage all health records)
Route::get('/mobile-api/health/teacher/data', '\App\Http\Controllers\MobileApiController@getTeacherHealthData');

// Create health records (staff only)
Route::post('/mobile-api/health/student/create-record', '\App\Http\Controllers\MobileApiController@createStudentHealthRecord');
Route::post('/mobile-api/health/staff/create-record', '\App\Http\Controllers\MobileApiController@createStaffHealthRecord');
Route::post('/mobile-api/health/guest/create-record', '\App\Http\Controllers\MobileApiController@createGuestHealthRecord');

// Update student health information (staff only)
Route::post('/mobile-api/health/student/update-info', '\App\Http\Controllers\MobileApiController@updateStudentHealthInfo');

// Delete health records (staff only)
Route::post('/mobile-api/health/delete-record', '\App\Http\Controllers\MobileApiController@deleteHealthRecord');

// Get lookup data for health forms (injuries, actions, medications)
Route::get('/mobile-api/health/lookup-data', '\App\Http\Controllers\MobileApiController@getHealthLookupData');

Route::get('/mobile-api/get-student-documents/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->studentFiles($_GET['authCode']);
});

// Alternative notification API implementation (if needed for specific use cases)
Route::get('/mobile-api/get-notifications-alt/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->mobileNotifications($_GET['username']);
});

Route::get('/mobile-api/get-student-homework/', function () {
    return view('partial_view.mobile.homework', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/get-student-homework-data/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->getStudentHomeworkData($_GET['authCode']);
});

// Student homework endpoints for mobile API
Route::post('/mobile-api/homework/mark-viewed', '\App\Http\Controllers\MobileApiController@markHomeworkAsViewed');
Route::post('/mobile-api/homework/submit', '\App\Http\Controllers\MobileApiController@submitHomework');
Route::post('/mobile-api/homework/mark-done', '\App\Http\Controllers\MobileApiController@markHomeworkAsDone');
Route::post('/mobile-api/homework/upload-file', '\App\Http\Controllers\MobileApiController@uploadHomeworkFile');
Route::post('/mobile-api/homework/update-submission', '\App\Http\Controllers\MobileApiController@updateHomeworkSubmission');

// Teacher homework endpoints for mobile API
Route::get('/mobile-api/teacher/homework/list', '\App\Http\Controllers\MobileApiController@getTeacherHomeworkList');
Route::get('/mobile-api/teacher/homework/details', '\App\Http\Controllers\MobileApiController@getTeacherHomeworkDetails');
Route::post('/mobile-api/teacher/homework/feedback', '\App\Http\Controllers\MobileApiController@provideHomeworkFeedback');
Route::post('/mobile-api/teacher/homework/create', '\App\Http\Controllers\MobileApiController@createHomeworkAssignment');
Route::post('/mobile-api/teacher/homework/close', '\App\Http\Controllers\MobileApiController@closeHomeworkAssignment');
Route::get('/mobile-api/teacher/homework/classes', '\App\Http\Controllers\MobileApiController@getTeacherClassesForHomework');
Route::get('/mobile-api/teacher/attendance/classes', '\App\Http\Controllers\MobileApiController@getTeacherClassesForAttendance');

// Homeroom teacher endpoints for mobile API
Route::get('/mobile-api/homeroom/classrooms', '\App\Http\Controllers\MobileApiController@getHomeroomClassrooms');
Route::get('/mobile-api/homeroom/students', '\App\Http\Controllers\MobileApiController@getHomeroomStudents');
Route::get('/mobile-api/homeroom/attendance', '\App\Http\Controllers\MobileApiController@getHomeroomAttendance');
Route::get('/mobile-api/homeroom/discipline', '\App\Http\Controllers\MobileApiController@getHomeroomDiscipline');
Route::get('/mobile-api/homeroom/student-profile', '\App\Http\Controllers\MobileApiController@getHomeroomStudentProfile');

Route::get('/mobile-api/check-device-data/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->checkDeviceData([
        'authCodes'     => $_GET['authCodes'],
        'deviceToken'   => $_GET['deviceToken']
    ]);
});

Route::get('/mobile-api/remove-user-from-device/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->removeUserFromDevice([
        'userId'        => $_GET['userId'],
        'deviceToken'   => $_GET['deviceToken']
    ]);
});

Route::get('/mobile-api/get-student-tracking/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->getStudentTracking($_GET['authCode']);
});

Route::get('/mobile-api/pickup-request/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->createPickupRequest([
        'authCode'  => $_GET['authCode'],
        'lat'       => $_GET['lat'],
        'lon'       => $_GET['lon']
    ]);
});

// DUPLICATE ROUTE REMOVED - This was a duplicate of the route above
// For HTML notification view, use a different route name
Route::get('/mobile-api/get-notifications/', function () {
    return view('partial_view.mobile.notification', ['username' => $_GET['username']]);
});

Route::get('/mobile-api/get-teacher-timetable/', function () {
    return view('partial_view.mobile.staff_timetable', ['authCode' => $_GET['authCode']]);
});
Route::post('/mobile-api/attendance/api-store', '\App\Http\Controllers\MobileApiController@storeApiClassAttendance');
//Route::post('/mobile-api/get-teacher-timetable/','\App\Http\Controllers\TimetableController@getDashboardTimetable');

Route::get('/mobile-api/get-teacher-bps/', function () {
    return view('partial_view.mobile.teacher-bps', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/get-teacher-assessments/', function () {
    return view('partial_view.mobile.teacher-assessment', ['authCode' => $_GET['authCode']]);
});
// Unified BPS storage route - handles both single and multiple BPS records
Route::post('/mobile-api/discipline/store-bps', '\App\Http\Controllers\MobileApiController@storeBps');
Route::delete('/mobile-api/discipline/delete-bps', ['middleware' => 'checkmobilepermissionaction:56-2', 'uses' => '\App\Http\Controllers\MobileApiController@deleteBps']);

// Check BPS delete permission (for frontend to show/hide delete buttons)
Route::get('/mobile-api/discipline/check-bps-delete-permission', '\App\Http\Controllers\MobileApiController@checkBpsDeletePermission');



// Legacy route for backward compatibility (redirects to unified method)
Route::post('/mobile-api/discipline/store-bps-multiple', '\App\Http\Controllers\MobileApiController@storeBps');

// New Teacher API endpoints for JSON data
Route::get('/mobile-api/get-teacher-timetable-data/', function () {
    try {
        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getTeacherTimetableData($_GET['authCode']);
    } catch (Exception $e) {
        return response()->json(['error' => 'Server error: ' . $e->getMessage()], 500);
    }
});

Route::get('/mobile-api/get-teacher-bps-data/', function () {
    try {
        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getTeacherBpsData($_GET['authCode']);
    } catch (Exception $e) {
        return response()->json(['error' => 'Server error: ' . $e->getMessage()], 500);
    }
});

Route::get('/mobile-api/get-attendance-details/', function () {
    try {
        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getAttendanceDetails($_GET['authCode'], $_GET['timetableId']);
    } catch (Exception $e) {
        return response()->json(['error' => 'Server error: ' . $e->getMessage()], 500);
    }
});

Route::get('/mobile-api/timetable-attendance/show', function () {
    return view('partial_view.mobile.timetable_attendance', ['timetableId' => $_GET['timetableId']]);
});
//new routes for new mobile app
Route::get('/mobile-api/about/', function () {
    return view('partial_view.mobile.about_info');
});

Route::get('/mobile-api/contacts/', function () {
    return view('partial_view.mobile.contacts');
});

Route::get('/mobile-api/finance-info/', function () {
    return view('partial_view.mobile.finance-info', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/faq/', function () {
    return view('partial_view.mobile.faq');
});

Route::get('/mobile-api/news/', function () {
    return view('partial_view.mobile.news', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/calendar/', function () {
    return view('partial_view.mobile.calendar');
});

Route::get('/mobile-api/gallery/', function () {
    return view('partial_view.mobile.gallery', ['authCode' => $_GET['authCode']]);
});

Route::get('/mobile-api/get-student-grades/', function () {
    $mobileApiRepository = new MobileApiRepository();
    return $mobileApiRepository->getStudentGrades($_GET['authCode']);
});

// Legacy notification mark as read route (for backward compatibility)
Route::post('/mobile-api/mark-notification-as-read/', '\App\Http\Controllers\Api\MobileNotificationController@markNotificationAsRead');

// Enhanced Mobile Notification API Routes
Route::prefix('mobile-api/notifications')->group(function () {
    // Get notifications for authenticated user
    Route::get('/list', '\App\Http\Controllers\Api\MobileNotificationController@getNotifications');

    // Mark notification as read
    Route::post('/mark-read', '\App\Http\Controllers\Api\MobileNotificationController@markAsRead');

    // Alternative route for marking notification as read (backward compatibility)
    Route::post('/mark-notification-as-read', '\App\Http\Controllers\Api\MobileNotificationController@markNotificationAsRead');

    // Mark all notifications as read
    Route::post('/mark-all-read', '\App\Http\Controllers\Api\MobileNotificationController@markAllAsRead');

    // Get notification categories
    Route::get('/categories', '\App\Http\Controllers\Api\MobileNotificationController@getCategories');

    // Send notification (staff only)
    Route::post('/send', '\App\Http\Controllers\Api\MobileNotificationController@sendNotification');

    // Get notification statistics (staff/admin only)
    Route::get('/statistics', '\App\Http\Controllers\Api\MobileNotificationController@getStatistics');




});

// Real-time notification endpoints
Route::prefix('mobile-api/notifications/realtime')->group(function () {
    // Send immediate BPS notification
    Route::post('/bps', function () {
        try {
            $mobileApiRepository = new MobileApiRepository();
            $notificationRepository = new \App\Library\Repository\MobileNotificationRepository(new \App\Library\Helper\AcademicHelper());

            $bpsData = request()->all();
            $result = $notificationRepository->sendBpsNotification($bpsData);

            return response()->json([
                'success' => $result,
                'message' => $result ? 'BPS notification sent successfully' : 'Failed to send BPS notification'
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => 'Server error: ' . $e->getMessage()], 500);
        }
    });

    // Send attendance reminder
    Route::post('/attendance-reminder', function () {
        try {
            $notificationRepository = new \App\Library\Repository\MobileNotificationRepository(new \App\Library\Helper\AcademicHelper());

            $data = request()->all();
            $result = $notificationRepository->sendAttendanceReminder($data);

            return response()->json([
                'success' => $result,
                'message' => $result ? 'Attendance reminder sent successfully' : 'Failed to send attendance reminder'
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => 'Server error: ' . $e->getMessage()], 500);
        }
    });

    // Send rich notification
    Route::post('/rich', function () {
        try {
            $notificationRepository = new \App\Library\Repository\MobileNotificationRepository(new \App\Library\Helper\AcademicHelper());

            $data = request()->all();
            $result = $notificationRepository->sendRichNotification($data);

            return response()->json([
                'success' => $result ? true : false,
                'message' => $result ? 'Rich notification created successfully' : 'Failed to create rich notification'
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => 'Server error: ' . $e->getMessage()], 500);
        }
    });

    // Send to staff
    Route::post('/staff', function () {
        try {
            $notificationRepository = new \App\Library\Repository\MobileNotificationRepository(new \App\Library\Helper\AcademicHelper());

            $data = request()->all();
            $result = $notificationRepository->sendToStaff($data);

            return response()->json([
                'success' => $result,
                'message' => $result ? 'Staff notification sent successfully' : 'Failed to send staff notification'
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => 'Server error: ' . $e->getMessage()], 500);
        }
    });
});




